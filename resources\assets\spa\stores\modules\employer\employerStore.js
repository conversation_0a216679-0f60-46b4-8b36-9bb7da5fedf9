import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import apiClient from '@spa/services/api.client.js';

export const useEmployerStore = defineStore('useEmployerStore', () => {
    const storeUrl = ref('v2/tenant/employer');

    // Form constants
    const statusOptions = ref([]);
    const industryOptions = ref([]);
    const countryOptions = ref([]);

    const {
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        submitKendoForm,
    } = useCommonStore(storeUrl.value);

    // Helper function to convert object to text-value format
    const convertToTextValue = (obj) => {
        return Object.entries(obj).map(([key, value]) => ({
            text: value,
            value: key,
        }));
    };

    // Method to fetch form constants
    const getFormConstants = async () => {
        try {
            const response = await apiClient.get(`/api/${storeUrl.value}/form-constants`);
            statusOptions.value = convertToTextValue(response.status_options);
            industryOptions.value = convertToTextValue(response.industry_options);
            countryOptions.value = convertToTextValue(response.country_options);
            return response;
        } catch (error) {
            console.error('Error fetching form constants:', error);
            throw error;
        }
    };

    return {
        // Common store methods
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        submitKendoForm,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        // Form constants and methods
        industryOptions,
        countryOptions,
        getFormConstants,
    };
});
