import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import apiClient from '@spa/services/api.client.js';

export const useEmployerStore = defineStore('useEmployerStore', () => {
    const storeUrl = ref('v2/tenant/employer');

    // Form constants
    const statusOptions = ref([]);
    const industryOptions = ref([]);
    const countryOptions = ref([]);

    const {
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        submitKendoForm,
    } = useCommonStore(storeUrl.value);

    // Helper function to convert object to text-value format
    const convertToTextValue = (obj) => {
        return Object.entries(obj).map(([key, value]) => ({
            text: value,
            value: key,
        }));
    };

    // Override the edit function to ensure proper data type conversion
    const editEmployer = async (item) => {
        // Ensure form constants are loaded before editing
        if (
            statusOptions.value.length === 0 ||
            countryOptions.value.length === 0 ||
            industryOptions.value.length === 0
        ) {
            await getFormConstants();
        }

        // Convert numeric values to strings to match dropdown value types
        // This is necessary because the constants use string keys but database values might be integers
        const editData = { ...item };
        if (editData.country_id !== null && editData.country_id !== undefined) {
            editData.country_id = String(editData.country_id);
        }
        if (editData.status !== null && editData.status !== undefined) {
            editData.status = String(editData.status);
        }
        if (editData.industries_id !== null && editData.industries_id !== undefined) {
            editData.industries_id = String(editData.industries_id);
        }

        // Call the original edit function
        edit(editData);
    };

    // Override the create function to ensure form constants are loaded
    const createEmployer = async () => {
        // Ensure form constants are loaded before creating
        if (
            statusOptions.value.length === 0 ||
            countryOptions.value.length === 0 ||
            industryOptions.value.length === 0
        ) {
            await getFormConstants();
        }

        // Call the original create function
        createFunction();
    };

    // Method to fetch form constants
    const getFormConstants = async () => {
        try {
            const response = await apiClient.get(`/api/${storeUrl.value}/form-constants`);
            statusOptions.value = convertToTextValue(response.status_options);
            industryOptions.value = convertToTextValue(response.industry_options);
            countryOptions.value = convertToTextValue(response.country_options);
            return response;
        } catch (error) {
            console.error('Error fetching form constants:', error);
            throw error;
        }
    };

    return {
        // Common store methods
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction: createEmployer, // Use our custom create function
        closeFunction,
        edit: editEmployer, // Use our custom edit function
        submitFormData,
        submitKendoForm,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        // Form constants and methods
        industryOptions,
        countryOptions,
        getFormConstants,
    };
});
