<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmployerRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'employer_name' => 'required|string|max:255',
            'trading_name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'ABN' => 'required|string|max:255',
            'address' => 'required|string',
            'industries_id' => 'nullable|string',
            'suburb' => 'required|string|max:255',
            'country_id' => 'nullable|integer',
            'email' => 'required|email|max:255',
            'fax' => 'nullable|string|max:20',
            'status' => 'required|integer',
            'postcode' => 'required|string|max:10',
            'state' => 'nullable|string|max:255',
            'phone' => 'required|string|max:20',
            'mobile' => 'nullable|string|max:20',
        ];
    }

    public function messages()
    {
        return [
            'employer_name.required' => 'Employer name is required.',
            'trading_name.required' => 'Trading name is required.',
            'contact_person.required' => 'Contact person is required.',
            'ABN.required' => 'ABN is required.',
            'address.required' => 'Address is required.',
            'suburb.required' => 'Suburb is required.',
            'email.required' => 'Email is required.',
            'email.email' => 'Please enter a valid email address.',
            'status.required' => 'Status is required.',
            'postcode.required' => 'Postcode is required.',
            'phone.required' => 'Phone number is required.',
        ];
    }
}
