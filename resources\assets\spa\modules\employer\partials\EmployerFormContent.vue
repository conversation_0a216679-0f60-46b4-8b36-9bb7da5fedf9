<template>
    <form-element>
        <fieldset class="h-screen-header flex flex-col">
            <div class="grid grid-cols-2 gap-4 p-6">
                <!-- Basic Information Fields -->
                <field
                    :id="'employer_name'"
                    :name="'employer_name'"
                    :label="'Employer Name'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['employer_name']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'trading_name'"
                    :name="'trading_name'"
                    :label="'Trading Name'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['trading_name']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'contact_person'"
                    :name="'contact_person'"
                    :label="'Contact Person'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['contact_person']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'ABN'"
                    :name="'ABN'"
                    :label="'ABN'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['ABN']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <!-- Contact Information Fields -->
                <field
                    :id="'email'"
                    :name="'email'"
                    :label="'Email'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['email']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            type="email"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'phone'"
                    :name="'phone'"
                    :label="'Phone'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['phone']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'mobile'"
                    :name="'mobile'"
                    :label="'Mobile'"
                    :component="'textTemplate'"
                    v-model="store.formData['mobile']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'fax'"
                    :name="'fax'"
                    :label="'Fax'"
                    :component="'textTemplate'"
                    v-model="store.formData['fax']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <!-- Address Information Fields -->
                <div class="col-span-2">
                    <field
                        :id="'address'"
                        :name="'address'"
                        :label="'Address'"
                        :component="'textareaTemplate'"
                        :validator="requiredtrue"
                        v-model="store.formData['address']"
                    >
                        <template #textareaTemplate="{ props }">
                            <FormTextArea
                                v-bind="props"
                                :rows="3"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>

                <field
                    :id="'suburb'"
                    :name="'suburb'"
                    :label="'Suburb'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['suburb']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'state'"
                    :name="'state'"
                    :label="'State'"
                    :component="'textTemplate'"
                    v-model="store.formData['state']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'postcode'"
                    :name="'postcode'"
                    :label="'Postcode'"
                    :component="'textTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['postcode']"
                >
                    <template #textTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'country_id'"
                    :name="'country_id'"
                    :label="'Country'"
                    :component="'dropdownTemplate'"
                    :data-items="store.countryOptions ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Country',
                        value: null,
                    }"
                    v-model="store.formData['country_id']"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <!-- Additional Information Fields -->
                <field
                    :id="'industries_id'"
                    :name="'industries_id'"
                    :label="'Industry'"
                    :component="'dropdownTemplate'"
                    :data-items="store.industryOptions ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Industry',
                        value: null,
                    }"
                    v-model="store.formData['industries_id']"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>

                <field
                    :id="'status'"
                    :name="'status'"
                    :label="'Status'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="store.statusOptions ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Status',
                        value: null,
                    }"
                    v-model="store.formData['status']"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
        </fieldset>
    </form-element>
</template>

<script setup>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormTextBox from '@spa/components/KendoInputs/FormTextBox.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { useEmployerStore } from '@spa/stores/modules/employer/employerStore.js';

const store = useEmployerStore();
</script>

<style scoped></style>
