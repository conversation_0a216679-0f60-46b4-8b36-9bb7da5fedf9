<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Auth;

class SetupServicesCategory extends Model {

    protected $table = 'rto_setup_services_category';

    public function addCategory($collegeId, $userId, $data) {

        $countResult = SetupServicesCategory::where('college_id', $collegeId)->where('category_name', $data['category_name'])->count();

        $objServices = new SetupServicesCategory();
        $objServices->college_id = $collegeId;
        $objServices->category_name = $data['category_name'];
        $objServices->service_name_id = $data['services_name'];
        $objServices->created_by = $userId;
        $objServices->updated_by = $userId;
        if ($countResult == 0) {
            $objServices->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Category Saved Successfully.';
            $returnData['lastId'] = $objServices->id;
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Category already exist';
        }
        return $returnData;
    }

    public function updateCategory($collegeId, $userId, $data) {

        $countResult = SetupServicesCategory::where('college_id', $collegeId)
                ->where('id', '!=', $data['categoryId'])
                ->where('category_name', $data['categoryName'])
                ->count();

        $objServices = SetupServicesCategory::find($data['categoryId']);
        $objServices->college_id = $collegeId;
        $objServices->category_name = $data['categoryName'];
        $objServices->created_by = $userId;
        $objServices->updated_by = $userId;
        if ($countResult == 0) {
            $objServices->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Category Update Successfully.';
            $returnData['lastId'] = $objServices->id;
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Category already exist';
        }
        return $returnData;
    }

    public function getCategoryName($college_id, $resultArr) {
        $resultArr = SetupServicesCategory::where('college_id', $college_id)
                        ->where('service_name_id', $resultArr['servicesId'])
                        ->get()->pluck('category_name', 'id')->toArray();
        $newArr = array('Add' => 'Add New category Name');
        if (count($resultArr) > 0) {
            return $resultArr + $newArr;
        } else {
            $result = array('' => 'No Data Found');
            return $result + $newArr;
        }
    }

    public function getCategoryNamev2($college_id, $resultArr) {
        $resultArr = SetupServicesCategory::where('college_id', $college_id)
                        ->where('service_name_id', $resultArr['servicesId'])
                        ->get()->pluck('category_name', 'id')->toArray();
        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            $result = array('' => 'No Data Found');
            return $result;
        }
    }

}