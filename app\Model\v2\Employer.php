<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class Employer extends Model
{
    protected $table = 'rto_employer';

    protected $fillable = [
        'college_id',
        'employer_name',
        'trading_name',
        'contact_person',
        'ABN',
        'industries_id',
        'address',
        'suburb',
        'country_id',
        'email',
        'fax',
        'postcode',
        'state',
        'phone',
        'mobile',
        'status',
    ];

    public function getEmployerNameList($collegeId)
    {
        $arrEmployerList = Employer::where('college_id', '=', $collegeId)->get(['id as Id', 'employer_name as Name'])->toArray();

        // $result[''] = '- - No Employer - -';
        // foreach ($arrEmployerList as $row) {
        //     $result[$row->id] = $row->employer_name;
        // }
        return $arrEmployerList;
    }

    // Relationship with industry
    public function industry()
    {
        return $this->belongsTo(\App\Model\v2\Industries::class, 'industries_id');
    }
}
